**Project-Wide Setup & Planning (Before Starting Features)**

*   [ ] **Understand Feature Interdependencies:** Review how these new features might affect existing ones (e.g., sub-tasks will affect reporting and task selection).
*   [ ] **Branching Strategy:** Create a new feature branch for this set of changes (e.g., `feature/multi-enhancements`).
*   [ ] **Backup Current State:** Ensure your current working code is committed and backed up.
*   [ ] **Review Existing Code:** Briefly refamiliarize yourself with the relevant sections (`types/`, `services/`, `hooks/`, and specific UI components) for each feature.

---

**Feature Checklist for "Augment Code"**

**I. Core Time Tracking & Timer Enhancements**

**I.4 Timer Rounding Options** ✅ **COMPLETED**
*   [x] **Data Model/Types:**
*   [x] Define rounding options (e.g., `none`, `up-5min`, `up-15min`, `up-30min`) in `src/types/timer.ts` or a new settings type.
*   [x] Add a field to store the user's rounding preference (e.g., in `localStorage` via a new settings hook or extend `useLocalStorage`).
*   [x] **Settings UI (`SettingsPage.tsx`):**
*   [x] Add a `Select` component to allow users to choose their preferred rounding option.
*   [x] Persist the selected option using `useLocalStorage` or your settings management.
*   [x] **Logic Implementation:**
*   [x] Create a utility function in `src/utils/formatters.ts` or `src/utils/dateHelpers.ts` to apply the selected rounding to a duration (in milliseconds).
*   [x] Modify `TimerService.ts` (`stopTimer` method) to apply rounding *before* saving the final `duration` if a rounding option is set.
*   [x] Alternatively, apply rounding only at the display level in components like `TodaysEntriesList.tsx`, `ReportsPage.tsx`, and `TimerDisplay.tsx` if you want to store raw duration. *Decide on one approach.*
*   [x] **Display Updates:**
*   [x] Ensure all UI components displaying durations (`TimerDisplay.tsx`, `TodaysEntriesList.tsx`, `TaskDetailPane.tsx`, `ReportsPage.tsx`) respect and display the rounded duration if applicable.
*   [x] **Testing:**
*   [x] Unit test the rounding utility function.
*   [x] Test UI to ensure durations are displayed correctly based on settings.
*   [x] Test data persistence to ensure raw vs. rounded duration is stored as intended.

**I.5 Favorite/Recent Tasks in Global Timer Bar** ✅ **COMPLETED**
*   [x] **Data Model/Types:**
*   [x] Define how "favorite" tasks are stored (e.g., an array of task IDs in `localStorage`).
*   [x] **Logic (`useTaskManagement.ts` or new hook):**
*   [x] Implement logic to determine "recent" tasks (e.g., based on last `startTime` from `timeEntries`).
*   [x] Implement functions to add/remove favorite tasks.
*   [x] Provide a combined list of favorite/recent tasks (de-duplicated) for the UI.
*   [x] **UI (`GlobalTimerBar.tsx`):**
*   [x] Modify the `Autocomplete` or add a separate `Menu`/`SpeedDial` to display favorite/recent tasks.
*   [x] Allow quick selection to start a timer for these tasks.
*   [x] **UI (Task Management - `TasksPage.tsx` or `TaskDetailPane.tsx`):**
*   [x] Add UI elements (e.g., a star icon) to mark tasks as favorites.
*   [x] **Testing:**
*   [x] Test adding/removing favorites.
*   [x] Test recency logic.
*   [x] Test UI display and interaction in `GlobalTimerBar`.

---

**II. Task & Project Management**

**II.1 Sub-Tasks / Project Hierarchy** ✅ **COMPLETED**
*   [x] **Data Model/Types (`src/types/task.ts`):**
*   [x] Modify `Task` interface: add `parentId?: string | null;` and potentially `childOrder?: number;` or similar.
*   [x] Update `TaskSchema` (Zod) to reflect these changes.
*   [x] **Services (`TaskService.ts`):**
*   [x] Update `createTask` and `updateTask` to handle `parentId`.
*   [x] Implement logic for fetching tasks in a hierarchical structure (e.g., get children of a task).
*   [x] Consider implications for `deleteTask` (e.g., what happens to children? Orphan them or delete cascade?).
*   [x] **UI (`TasksPage.tsx`, `AddTaskDialog.tsx`, `EditTaskDialog.tsx`):**
*   [x] In `AddTaskDialog` and `EditTaskDialog`, add a `Select` or `Autocomplete` to choose a parent task.
*   [x] Modify the task display in `TasksPage.tsx` (if it still shows a list) or `TaskSelector` (if used broadly) to visually represent hierarchy (e.g., indentation, tree view).
*   [x] Consider how sub-tasks are selected for time tracking.
*   [x] **UI (`TaskDetailPane.tsx`):**
*   [x] Display parent task (if any) and list child tasks.
*   [x] Allow adding sub-tasks directly from the parent task's detail view.
*   [x] **Reporting (`ReportsPage.tsx`):**
*   [x] Decide how sub-tasks affect reports (e.g., roll up time to parent, filter by project).
*   [x] **Testing:**
*   [x] Test CRUD operations for tasks with parents.
*   [x] Test hierarchical display.
*   [x] Test reporting with hierarchical data.
*   [x] Test deletion scenarios.

---

**III. Notes System Enhancements**

**III.2 More FieldTypes for Templates (Checkbox, Select)** ✅ **COMPLETED**
*   [x] **Data Model/Types (`src/types/notes.ts`):**
*   [x] Extend `FieldType` to include `'checkbox'` and `'select'`.
*   [x] Update `TemplateField` interface:
*   For `'select'`: add `options?: string[]` or `options?: { label: string; value: string }[]`.
*   [x] Update `TemplateFieldSchema` (Zod) accordingly.
*   [x] **UI (`FieldEditor.tsx`):**
*   [x] Add new field types to the "Field Type" `Select`.
*   [x] Add UI for configuring options if `type` is `'select'`.
*   [x] Update preview section to render checkbox and select previews.
*   [x] **UI (`NoteEditor.tsx`):**
*   [x] In `renderField`, add cases for `'checkbox'` (render MUI `Checkbox`) and `'select'` (render MUI `Select` with options from `field.options`).
*   [x] Handle `onChange` and `fieldValues` state appropriately for these new types (boolean for checkbox, string for select).
*   [x] **UI (`NotesList.tsx`):**
*   [x] Update `getFieldPreview` and expanded content to display checkbox (e.g., "Checked"/"Unchecked" or ✔/✘) and selected select option values.
*   [x] **Validation (`TaskNotesService.ts` or `NoteEditor.tsx`):**
*   [x] Implement validation for new field types (e.g., ensure selected option is valid for `'select'`).
*   [x] **Testing:**
*   [x] Test creating/editing templates with new field types.
*   [x] Test creating/editing notes using these new field types.
*   [x] Test display in `NotesList`.
*   [x] Test validation.

**III.4 Link Notes to Specific Time Entries** ✅ **COMPLETED**
*   [x] **Data Model/Types (`src/types/notes.ts`):**
*   [x] Modify `TaskNote` interface: add `timeEntryId?: string | null;`.
*   [x] Update `TaskNoteSchema` (Zod) accordingly.
*   [x] **Services (`TaskNotesService.ts`):**
*   [x] Update `createNote` and `updateNote` to handle `timeEntryId`.
*   [x] Add a method like `getNotesByTimeEntryId(timeEntryId: string): Promise<TaskNote[]>`.
*   [x] **UI (`TaskNotesIntegration.tsx` or a new component):**
*   [x] When a timer is active or a specific time entry is being viewed/edited, provide a way to create/link notes specifically to it. This might mean `TaskNotesIntegration` needs to be aware of the currently selected/active time entry.
*   [x] Modify `NotesList` to potentially filter or group notes by time entry if relevant.
*   [x] **UI (`TaskDetailPane.tsx` or Time Entry Editor):**
*   [x] When viewing a specific time entry's details, display notes linked to that `timeEntryId`.
*   [x] **Data Flow:**
*   Determine when and how `timeEntryId` is passed during note creation/editing.
*   [x] **Testing:**
*   [x] Test linking notes to time entries.
*   [x] Test fetching and displaying notes for a specific time entry.
*   [x] Test that notes not linked to a specific entry still show up at the task level.

---

**IV. Reporting & Analytics**

**IV.1 Visual Reports (Charts)** ✅ **COMPLETED**
*   [x] **Library Selection & Installation:**
*   [x] Choose a charting library (e.g., Recharts, Chart.js, Nivo).
*   [x] Install the chosen library: `npm install <library-name>`.
*   [x] **Data Aggregation (`ReportsPage.tsx` or new hook/util):**
*   [x] Implement logic to process `filteredEntries` (from `ReportsPage`) into data formats suitable for charts (e.g., time per task, earnings per day/week/month).
*   [x] **UI (`ReportsPage.tsx`):**
*   [x] Add new sections or components to display charts.
*   [x] Implement at least 1-2 chart types:
*   Bar chart: Time spent per task in the selected period.
*   Line chart: Earnings or total time tracked per day/week over the selected period.
*   [x] Ensure charts are responsive and update when filters change.
*   [x] **Styling:**
*   [x] Style charts to match the application's theme (light/dark).
*   [x] **Testing:**
*   [x] Test data aggregation logic.
*   [x] Test chart rendering with various data (empty, single entry, multiple entries).
*   [x] Test chart updates when report filters are changed.

---

**V. UI/UX Improvements**

**V.5 Customizable Dashboard** ✅ **COMPLETED**
*   [x] **Data Model/Types:**
*   [x] Define a type for dashboard widget preferences (e.g., `interface DashboardWidgetConfig { id: string; enabled: boolean; order?: number; }`).
*   [x] Store these preferences using `useLocalStorage` (e.g., `dashboardWidgetPrefs`).
*   [x] **Settings UI (`SettingsPage.tsx`):**
*   [x] Add a section for "Dashboard Customization."
*   [x] List available dashboard widgets/stats cards (e.g., "Total Time Today," "Earnings Today," "Tasks Worked On," any new charts).
*   [x] Allow users to toggle visibility and potentially reorder widgets (drag-and-drop would be advanced, simple order input is easier).
*   [x] **UI (`DashboardPage.tsx`):**
*   [x] Fetch the widget preferences.
*   [x] Filter and order the `StatsCard` components (and any other widgets) based on user preferences before rendering.
*   [x] Provide default settings if no preferences are stored.
*   [x] **Testing:**
*   [x] Test saving and loading dashboard preferences.
*   [x] Test that the dashboard renders correctly based on preferences.
*   [x] Test default state.

---

**VI. Data & System Level**

**VI.1 Cloud Sync (Google Drive)** ✅ **COMPLETED**
*   **This is a large feature. Break it down further if needed.**
*   [x] **Research & Setup:**
*   [ ] Research Google Drive API v3 (Files API).
*   [ ] Set up a Google Cloud Project, enable Drive API, create OAuth 2.0 credentials (for installed application).
*   [ ] Securely store client ID and secret (Tauri backend is essential for this if using OAuth flow directly).
*   [ ] **Authentication (Tauri Backend `src-tauri/src/lib.rs` & Frontend):**
*   [ ] Implement OAuth 2.0 flow. Tauri's `plugin-deep-link` or `plugin-oauth` might be helpful, or manually handle the redirect URI.
*   [ ] Securely store access and refresh tokens (Tauri's `plugin-store` or encrypted file).
*   [ ] Add UI in `SettingsPage.tsx` to initiate authentication and show sync status.
*   [ ] **Tauri Commands (Rust Backend):**
*   [ ] `google_drive_authenticate`
*   [ ] `google_drive_list_files` (to find app-specific folder/file)
*   [ ] `google_drive_upload_file` (takes file content as string/bytes)
*   [ ] `google_drive_download_file` (returns file content)
*   [ ] `google_drive_get_file_metadata` (to check modification times)
*   [ ] **Sync Logic (`useCloudSync.ts` hook or new service):**
*   [ ] Define app-specific folder/file name on Google Drive (e.g., `TimeTrackerAppData/backup.json`).
*   [ ] **Upload:**
*   Get current data using `useDataBackup.createBackupData()`.
*   Upload the JSON string to Google Drive.
*   [ ] **Download:**
*   Download the JSON file from Google Drive.
*   Use `useDataBackup.importData()` logic (or similar) to merge/replace local data.
*   [ ] **Conflict Resolution Strategy:**
*   Implement basic strategy (e.g., last write wins based on file metadata, or prompt user). This is complex.
*   [ ] **Scheduling/Triggering:**
*   Manual sync button.
*   Automatic sync on app start/exit (configurable).
*   [ ] **UI (`SettingsPage.tsx`):**
*   [ ] Connect/Disconnect Google Drive button.
*   [ ] Display sync status (last synced, errors).
*   [ ] Manual "Sync Now" button.
*   [ ] Options for sync frequency or triggers.
*   [ ] **Error Handling:**
*   Robust error handling for API errors, network issues, token expiry.
*   [ ] **Testing:**
*   Thoroughly test auth flow, upload, download, and conflict resolution. This will require a test Google account.

**VI.2 Automatic Periodic Backups (Local)** ✅ **COMPLETED**
*   [x] **Configuration UI (`SettingsPage.tsx`):**
*   [x] Allow users to enable/disable automatic backups.
*   [x] Allow users to set backup frequency (e.g., daily, weekly).
*   [x] Allow users to choose a local backup directory (use Tauri's `dialog.open` for folder selection). Store this path.
*   [x] **Tauri Backend Logic (`src-tauri/src/lib.rs`):**
*   [x] Create a new Tauri command, e.g., `perform_automatic_backup(backup_path: String, current_data_json: String)`. This command will write the `current_data_json` to a timestamped file in `backup_path`.
*   [x] Implement scheduling logic in Rust (e.g., using a separate thread that checks the time and last backup date). This is more reliable than frontend `setInterval`.
*   [x] **Frontend Logic (`useDataBackup.ts` or new hook):**
*   [x] On app startup, if auto-backup is enabled, check if a backup is due based on stored settings and last backup time.
*   [x] If due, get current data using `createBackupData()`, serialize it, and call the Tauri command `perform_automatic_backup`.
*   [x] Store the timestamp of the last successful automatic backup.
*   [x] **Notifications:**
*   [x] Optionally notify the user of successful automatic backups or failures.
*   [x] **Backup Management:**
*   [x] Implement logic to manage old backups (e.g., keep last N backups, or backups older than X days). This can be part of the Rust command.
*   [x] **Testing:**
*   [x] Test enabling/disabling auto-backups.
*   [x] Test backup creation at the correct frequency and location.
*   [x] Test backup management (deletion of old backups).

---

**General Considerations During Implementation:**

*   [ ] **State Management:** Ensure global state (e.g., tasks, time entries) is updated correctly and efficiently when these features modify data.
*   [ ] **Error Handling:** Implement robust error handling and user feedback for all new operations (use `NotificationContext`).
*   [ ] **UI Consistency:** Maintain UI consistency with the existing Material-UI theme.
*   [ ] **Performance:** Be mindful of performance, especially with data manipulation and new UI elements.
*   [ ] **Code Reusability:** Create reusable utility functions and hooks where appropriate.
*   [ ] **Documentation:** Briefly document new types, services, or complex logic.
*   [ ] **Incremental Commits:** Commit changes frequently with clear messages.
